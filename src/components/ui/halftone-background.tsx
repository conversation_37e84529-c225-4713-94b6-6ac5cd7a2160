import React, { useEffect, useRef, useState, useCallback } from 'react';
import { useTheme } from '@/context/ThemeContext';

interface HalftoneBackgroundProps {
  className?: string;
}

/**
 * Dynamic gradient halftone background component with mouse-following animation
 * Features:
 * - Halftone pattern using CSS and SVG
 * - Mouse-following gradient animation
 * - Performance-optimized with requestAnimationFrame
 * - Respects prefers-reduced-motion
 * - Theme-aware colors
 */
export const HalftoneBackground: React.FC<HalftoneBackgroundProps> = ({ 
  className = '' 
}) => {
  const { theme } = useTheme();
  const containerRef = useRef<HTMLDivElement>(null);
  const animationRef = useRef<number>();
  const [mousePosition, setMousePosition] = useState({ x: 0.5, y: 0.5 });
  const [prefersReducedMotion, setPrefersReducedMotion] = useState(false);
  const [isMobile, setIsMobile] = useState(false);

  // Check for reduced motion preference and mobile device
  useEffect(() => {
    const reducedMotionQuery = window.matchMedia('(prefers-reduced-motion: reduce)');
    const mobileQuery = window.matchMedia('(max-width: 768px)');

    setPrefersReducedMotion(reducedMotionQuery.matches);
    setIsMobile(mobileQuery.matches);

    const handleReducedMotionChange = () => setPrefersReducedMotion(reducedMotionQuery.matches);
    const handleMobileChange = () => setIsMobile(mobileQuery.matches);

    reducedMotionQuery.addEventListener('change', handleReducedMotionChange);
    mobileQuery.addEventListener('change', handleMobileChange);

    return () => {
      reducedMotionQuery.removeEventListener('change', handleReducedMotionChange);
      mobileQuery.removeEventListener('change', handleMobileChange);
    };
  }, []);

  // Mouse tracking with throttled updates and smooth interpolation
  const handleMouseMove = useCallback((event: MouseEvent) => {
    if (prefersReducedMotion || isMobile) return;

    // Use viewport coordinates for global tracking
    const x = event.clientX / window.innerWidth;
    const y = event.clientY / window.innerHeight;

    // Use requestAnimationFrame for smooth updates
    if (animationRef.current) {
      cancelAnimationFrame(animationRef.current);
    }

    animationRef.current = requestAnimationFrame(() => {
      // Smooth interpolation for more fluid movement
      setMousePosition(prev => ({
        x: Math.max(0, Math.min(1, prev.x + (x - prev.x) * 0.15)),
        y: Math.max(0, Math.min(1, prev.y + (y - prev.y) * 0.15))
      }));
    });
  }, [prefersReducedMotion, isMobile]);

  // Set up mouse tracking with global event listener for better performance
  useEffect(() => {
    if (prefersReducedMotion || isMobile) return;

    // Use global mouse tracking for better performance
    document.addEventListener('mousemove', handleMouseMove, { passive: true });

    return () => {
      document.removeEventListener('mousemove', handleMouseMove);
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
    };
  }, [handleMouseMove, prefersReducedMotion, isMobile]);

  // Generate halftone pattern SVG with mobile optimization
  const generateHalftonePattern = () => {
    const dotSize = isMobile ? 3 : 4;
    const spacing = isMobile ? 10 : 8;
    const rows = isMobile ? 15 : 20;
    const cols = isMobile ? 20 : 30;

    const dots = [];
    for (let row = 0; row < rows; row++) {
      for (let col = 0; col < cols; col++) {
        const x = col * spacing + (row % 2) * (spacing / 2);
        const y = row * spacing;
        
        // Vary dot size based on position for organic feel
        const distanceFromCenter = Math.sqrt(
          Math.pow((col - cols/2) / cols, 2) + 
          Math.pow((row - rows/2) / rows, 2)
        );
        const sizeVariation = 0.5 + 0.5 * (1 - distanceFromCenter);
        const finalSize = dotSize * sizeVariation;

        dots.push(
          <circle
            key={`${row}-${col}`}
            cx={x}
            cy={y}
            r={finalSize}
            fill="currentColor"
            opacity={0.1 + 0.2 * sizeVariation}
          />
        );
      }
    }

    return dots;
  };

  // Dynamic gradient styles based on mouse position and theme
  const getGradientStyle = () => {
    const { x, y } = mousePosition;
    
    if (theme === 'dark') {
      return {
        background: `
          radial-gradient(
            circle at ${x * 100}% ${y * 100}%, 
            rgba(53, 219, 113, 0.15) 0%, 
            rgba(53, 219, 113, 0.08) 25%, 
            rgba(15, 15, 15, 0.95) 50%,
            rgba(15, 15, 15, 1) 100%
          ),
          linear-gradient(
            ${135 + x * 30}deg, 
            rgba(15, 15, 15, 1) 0%, 
            rgba(22, 22, 22, 0.98) 25%,
            rgba(28, 28, 28, 0.95) 50%,
            rgba(15, 15, 15, 1) 100%
          )
        `,
        transition: prefersReducedMotion ? 'none' : 'background 0.3s ease-out'
      };
    } else {
      return {
        background: `
          radial-gradient(
            circle at ${x * 100}% ${y * 100}%, 
            rgba(53, 219, 113, 0.08) 0%, 
            rgba(53, 219, 113, 0.04) 25%, 
            rgba(244, 242, 237, 0.95) 50%,
            rgba(244, 242, 237, 1) 100%
          ),
          linear-gradient(
            ${45 + x * 30}deg, 
            rgba(244, 242, 237, 1) 0%, 
            rgba(248, 246, 241, 0.98) 25%,
            rgba(252, 250, 245, 0.95) 50%,
            rgba(244, 242, 237, 1) 100%
          )
        `,
        transition: prefersReducedMotion ? 'none' : 'background 0.3s ease-out'
      };
    }
  };

  return (
    <div
      ref={containerRef}
      className={`fixed inset-0 pointer-events-none z-0 overflow-hidden ${className}`}
      style={getGradientStyle()}
    >
      {/* Halftone Pattern Overlay */}
      <div
        className="absolute inset-0 opacity-30 halftone-pattern"
        style={{
          transform: (prefersReducedMotion || isMobile) ? 'none' : `translate(${mousePosition.x * 8 - 4}px, ${mousePosition.y * 8 - 4}px) rotate(${mousePosition.x * 2 - 1}deg)`,
          transition: (prefersReducedMotion || isMobile) ? 'none' : 'transform 0.25s ease-out'
        }}
      >
        <svg
          className={`w-full h-full ${theme === 'dark' ? 'text-soccer-primary' : 'text-soccer-primary-dark'}`}
          style={{
            filter: isMobile ? 'none' : 'blur(0.5px)',
            opacity: isMobile ? 0.3 : (0.4 + mousePosition.y * 0.2)
          }}
          viewBox={isMobile ? "0 0 200 150" : "0 0 240 160"}
          preserveAspectRatio="xMidYMid slice"
        >
          {generateHalftonePattern()}
        </svg>
      </div>

      {/* Additional gradient overlay for depth */}
      <div
        className="absolute inset-0 opacity-20"
        style={{
          background: theme === 'dark'
            ? 'radial-gradient(ellipse at center, transparent 0%, rgba(15, 15, 15, 0.8) 100%)'
            : 'radial-gradient(ellipse at center, transparent 0%, rgba(244, 242, 237, 0.8) 100%)',
          transform: (prefersReducedMotion || isMobile) ? 'none' : `scale(${1 + mousePosition.y * 0.1})`,
          transition: (prefersReducedMotion || isMobile) ? 'none' : 'transform 0.4s ease-out'
        }}
      />
    </div>
  );
};
